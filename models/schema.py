"""
Schema Models
------------
Pydantic models for structured data in the story generation pipeline.
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Optional


class CharacterSketch(BaseModel):
    """Character sketch for a story character."""
    name: str = <PERSON>(
        description="Name of the character"
    )
    role: str = Field(
        description="Role of the character (protagonist, antagonist, supporting)"
    )
    background: str = Field(
        description="Background information about the character"
    )
    motivation: str = Field(
        description="Motivation or driving force of the character"
    )


class Setting(BaseModel):
    """Setting description for a story location."""
    location: str = Field(
        description="Name or description of the location"
    )
    description: str = Field(
        description="Detailed description of the location"
    )
    atmosphere: str = Field(
        description="Atmosphere or mood of the location"
    )


class ResearchData(BaseModel):
    """Research data structure for both real and fictional stories."""
    background_information: str = Field(
        description="Background information about the topic or story setting"
    )
    key_events: List[str] = Field(
        default_factory=list,
        description="List of key events or plot points"
    )
    cultural_context: str = Field(
        default="",
        description="Cultural context relevant to the story"
    )
    potential_story_elements: List[str] = Field(
        default_factory=list,
        description="Potential elements to include in the story"
    )
    possible_plot_twists: List[str] = Field(
        default_factory=list,
        description="Possible plot twists or unexpected developments"
    )
    character_sketches: List[CharacterSketch] = Field(
        default_factory=list,
        description="Character sketches for the story"
    )
    settings: List[Setting] = Field(
        default_factory=list,
        description="Settings or locations for the story"
    )


class EnhancedResearchData(ResearchData):
    """Enhanced research data structure with additional narrative elements."""
    narrative_structure: str = Field(
        default="",
        description="Refined narrative structure and flow for the story"
    )
    thematic_elements: List[str] = Field(
        default_factory=list,
        description="Key thematic elements and motifs in the story"
    )
    emotional_arcs: List[str] = Field(
        default_factory=list,
        description="Emotional arcs and character development paths"
    )
    dialogue_suggestions: List[str] = Field(
        default_factory=list,
        description="Suggested dialogue styles and key exchanges"
    )
    cultural_authenticity_notes: str = Field(
        default="",
        description="Notes on ensuring cultural authenticity in the story"
    )


class Scene(BaseModel):
    """A scene in the story."""
    scene_number: int = Field(
        description="Sequential number of the scene"
    )
    narration: str = Field(
        description="Hindi narration text for this scene (MUST be in Devanagari script)"
    )
    visual_description: str = Field(
        description="Description in English of what should be shown visually during this narration"
    )
    transition_from_previous: str = Field(
        description="Description in English of how this scene transitions from the previous scene",
        default=""
    )
    narrative_purpose: str = Field(
        description="The purpose of this scene in English in the overall narrative (e.g., hook, character introduction, plot development, climax, resolution)",
        default=""
    )


class Story(BaseModel):
    """Complete story structure with scenes."""
    title: str = Field(
        description="Title of the story (can be in Hindi or English, depending on user input)"
    )
    scenes: List[Scene] = Field(
        description="List of scenes in the story"
    )


class SceneSegment(BaseModel):
    """A segment of a scene for narration and image generation."""
    scene_number: int = Field(
        description="Scene number this segment belongs to"
    )
    segment_number: int = Field(
        description="Sequential number of this segment within the scene"
    )
    narration: str = Field(
        description="Hindi narration text for this segment (MUST be in Devanagari script)"
    )
    visual_cue: str = Field(
        description="Description in English of what should be shown visually during this segment"
    )
    estimated_duration_seconds: int = Field(
        description="Estimated duration of this segment in seconds",
        default=20
    )


class ImagePrompt(BaseModel):
    """Image prompt for a scene segment."""
    scene_number: int = Field(
        description="Scene number this image belongs to"
    )
    segment_number: int = Field(
        description="Segment number this image belongs to"
    )
    narration: str = Field(
        description="Hindi narration text for this segment"
    )
    visual_cue: str = Field(
        description="Description of what should be shown visually during this segment"
    )
    image_prompt: str = Field(
        description="Detailed image prompt for image generation"
    )
    estimated_duration_seconds: int = Field(
        description="Estimated duration of this segment in seconds",
        default=20
    )


class SceneSegmentList(BaseModel):
    """A list of scene segments."""
    segments: List[SceneSegment] = Field(
        description="List of scene segments"
    )


class ImagePromptList(BaseModel):
    """A list of image prompts."""
    prompts: List[ImagePrompt] = Field(
        description="List of image prompts"
    )


class Character(BaseModel):
    """Detailed character profile for visual consistency."""
    name: str = Field(
        description="Name of the character"
    )
    role: str = Field(
        description="Role in the story (protagonist, antagonist, supporting character, etc.)"
    )
    physical_description: str = Field(
        description="Detailed physical appearance including age, height, build, hair, eyes, skin tone, etc."
    )
    clothing_style: str = Field(
        description="Typical clothing style and attire"
    )
    distinctive_features: str = Field(
        description="Unique or distinctive physical features that make the character recognizable"
    )
    personality_traits: str = Field(
        description="Key personality traits that might affect visual representation"
    )
    background: str = Field(
        description="Brief background information relevant to visual representation"
    )
    scenes_appeared: List[int] = Field(
        default_factory=list,
        description="List of scene numbers where this character appears"
    )


class CharacterReference(BaseModel):
    """Reference image data for a character."""
    character_name: str = Field(
        description="Name of the character this reference belongs to"
    )
    reference_image_path: str = Field(
        description="Path to the reference image file"
    )
    image_prompt_used: str = Field(
        description="The prompt used to generate this reference image"
    )
    generation_timestamp: str = Field(
        description="Timestamp when the reference image was generated"
    )


class CharacterConsistencyData(BaseModel):
    """Complete character consistency data for a story."""
    characters: List[Character] = Field(
        description="List of all characters identified in the story"
    )
    character_references: List[CharacterReference] = Field(
        description="List of reference images for each character"
    )
    character_appearance_notes: Dict[str, str] = Field(
        default_factory=dict,
        description="Additional notes about character appearances and consistency requirements"
    )
    characters_group_image_path: Optional[str] = Field(
        default=None,
        description="Path to the group reference image for appearance-based character consistency"
    )
    has_labeled_reference: bool = Field(
        default=False,
        description="Whether the group reference image uses appearance-based matching (always False for appearance-based system)"
    )


class EnhancedImagePrompt(ImagePrompt):
    """Enhanced image prompt with character consistency information."""
    characters_in_scene: List[str] = Field(
        default_factory=list,
        description="Names of characters that appear in this scene"
    )
    character_descriptions: Dict[str, str] = Field(
        default_factory=dict,
        description="Character-specific physical appearance descriptions for this scene"
    )
    character_reference_paths: Dict[str, str] = Field(
        default_factory=dict,
        description="Paths to character reference images"
    )
