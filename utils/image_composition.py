"""
Image Composition Utility
------------------------
Utilities for composing character reference images using PIL.
"""

import logging
from typing import List
from PIL import Image

logger = logging.getLogger(__name__)


class ImageComposer:
    """
    Utility class for composing character reference images.

    This class provides functionality to compose multiple character images
    into a single group reference image for appearance-based character consistency.
    """
    
    def __init__(self):
        """Initialize the image composer."""
        self.background_color = (255, 255, 255)  # White background
        

    
    def compose_group_image(self,
                           image_paths: List[str],
                           output_path: str,
                           layout: str = 'horizontal') -> bool:
        """
        Compose multiple character images into a single group reference image.

        Args:
            image_paths (List[str]): List of paths to character images
            output_path (str): Path to save the composed group image
            layout (str): Layout type - 'horizontal', 'vertical', or 'grid'

        Returns:
            bool: True if successful, False otherwise
        """
        if not image_paths:
            logger.warning("No images provided for group composition")
            return False
            
        try:
            # Load all images
            images = []
            for img_path in image_paths:
                try:
                    with Image.open(img_path) as img:
                        images.append(img.convert('RGB').copy())
                except Exception as e:
                    logger.error(f"Error loading image {img_path}: {str(e)}")
                    continue
            
            if not images:
                logger.error("No valid images loaded for group composition")
                return False
            
            # Calculate dimensions based on layout
            if layout == 'horizontal':
                group_img = self._compose_horizontal(images)
            elif layout == 'vertical':
                group_img = self._compose_vertical(images)
            elif layout == 'grid':
                group_img = self._compose_grid(images)
            else:
                logger.error(f"Unsupported layout: {layout}")
                return False
            
            # Save the composed image
            group_img.save(output_path, 'PNG', quality=95)
            logger.info(f"Composed group image saved to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error composing group image: {str(e)}")
            return False
    
    def _compose_horizontal(self, images: List[Image.Image]) -> Image.Image:
        """Compose images horizontally."""
        # Find the maximum height and total width
        max_height = max(img.height for img in images)
        total_width = sum(img.width for img in images)
        
        # Add spacing between images
        spacing = 20
        total_width += spacing * (len(images) - 1)
        
        # Create the group image with landscape aspect ratio (16:9)
        target_width = 1920
        target_height = 1080
        
        # Scale if necessary to fit target dimensions
        if total_width > target_width or max_height > target_height:
            scale_factor = min(target_width / total_width, target_height / max_height)
            images = [img.resize((int(img.width * scale_factor), int(img.height * scale_factor)), 
                               Image.Resampling.LANCZOS) for img in images]
            max_height = max(img.height for img in images)
            total_width = sum(img.width for img in images) + spacing * (len(images) - 1)
        
        # Create the final image
        group_img = Image.new('RGB', (target_width, target_height), self.background_color)
        
        # Calculate starting position to center the composition
        start_x = (target_width - total_width) // 2
        start_y = (target_height - max_height) // 2
        
        # Paste images
        current_x = start_x
        for img in images:
            y_offset = start_y + (max_height - img.height) // 2
            group_img.paste(img, (current_x, y_offset))
            current_x += img.width + spacing
        
        return group_img
    
    def _compose_vertical(self, images: List[Image.Image]) -> Image.Image:
        """Compose images vertically."""
        # Find the maximum width and total height
        max_width = max(img.width for img in images)
        total_height = sum(img.height for img in images)
        
        # Add spacing between images
        spacing = 20
        total_height += spacing * (len(images) - 1)
        
        # Create the group image with landscape aspect ratio (16:9)
        target_width = 1920
        target_height = 1080
        
        # Scale if necessary to fit target dimensions
        if max_width > target_width or total_height > target_height:
            scale_factor = min(target_width / max_width, target_height / total_height)
            images = [img.resize((int(img.width * scale_factor), int(img.height * scale_factor)), 
                               Image.Resampling.LANCZOS) for img in images]
            max_width = max(img.width for img in images)
            total_height = sum(img.height for img in images) + spacing * (len(images) - 1)
        
        # Create the final image
        group_img = Image.new('RGB', (target_width, target_height), self.background_color)
        
        # Calculate starting position to center the composition
        start_x = (target_width - max_width) // 2
        start_y = (target_height - total_height) // 2
        
        # Paste images
        current_y = start_y
        for img in images:
            x_offset = start_x + (max_width - img.width) // 2
            group_img.paste(img, (x_offset, current_y))
            current_y += img.height + spacing
        
        return group_img
    
    def _compose_grid(self, images: List[Image.Image]) -> Image.Image:
        """Compose images in a grid layout."""
        num_images = len(images)
        
        # Calculate grid dimensions
        if num_images <= 2:
            cols = num_images
            rows = 1
        elif num_images <= 4:
            cols = 2
            rows = 2
        elif num_images <= 6:
            cols = 3
            rows = 2
        else:
            cols = 3
            rows = (num_images + cols - 1) // cols  # Ceiling division
        
        # Find the maximum dimensions
        max_width = max(img.width for img in images)
        max_height = max(img.height for img in images)
        
        # Calculate spacing
        spacing = 20
        
        # Calculate total dimensions
        total_width = cols * max_width + (cols - 1) * spacing
        total_height = rows * max_height + (rows - 1) * spacing
        
        # Target dimensions (landscape 16:9)
        target_width = 1920
        target_height = 1080
        
        # Scale if necessary
        if total_width > target_width or total_height > target_height:
            scale_factor = min(target_width / total_width, target_height / total_height)
            images = [img.resize((int(img.width * scale_factor), int(img.height * scale_factor)), 
                               Image.Resampling.LANCZOS) for img in images]
            max_width = max(img.width for img in images)
            max_height = max(img.height for img in images)
            total_width = cols * max_width + (cols - 1) * spacing
            total_height = rows * max_height + (rows - 1) * spacing
        
        # Create the final image
        group_img = Image.new('RGB', (target_width, target_height), self.background_color)
        
        # Calculate starting position to center the grid
        start_x = (target_width - total_width) // 2
        start_y = (target_height - total_height) // 2
        
        # Paste images in grid
        for i, img in enumerate(images):
            row = i // cols
            col = i % cols
            
            x = start_x + col * (max_width + spacing)
            y = start_y + row * (max_height + spacing)
            
            # Center the image in its cell
            x_offset = x + (max_width - img.width) // 2
            y_offset = y + (max_height - img.height) // 2
            
            group_img.paste(img, (x_offset, y_offset))
        
        return group_img


# Global instance for easy access
image_composer = ImageComposer()


def compose_group_image(image_paths: List[str],
                       output_path: str,
                       layout: str = 'horizontal') -> bool:
    """
    Convenience function to compose a group image.

    Args:
        image_paths (List[str]): List of paths to character images
        output_path (str): Path to save the composed group image
        layout (str): Layout type - 'horizontal', 'vertical', or 'grid'

    Returns:
        bool: True if successful, False otherwise
    """
    return image_composer.compose_group_image(image_paths, output_path, layout)
